package com.mall.project.dao.user;

import com.mall.project.exception.BusinessException;
import com.mall.project.util.MD5Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户数据访问层
 */
@Repository
public class UserDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private MD5Util md5Util;

    /**
     * 检查手机号是否存在
     */
    public boolean checkMobileExists(String mobile) {
        if (mobile == null || mobile.trim().isEmpty()) {
            throw new BusinessException("手机号不能为空");
        }
        
        String sql = "SELECT EXISTS(SELECT 1 FROM employees WHERE phone = ?)";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, mobile);
        return count != null && count > 0;
    }

    /**
     * 查询职员编号是否存在
     */
    public boolean checkEmployeeNoExists(Long employeeNo) {
        if (employeeNo == null) {
            throw new BusinessException("职员编号不能为空");
        }
        
        String sql = "SELECT EXISTS (SELECT 1 FROM employees WHERE employee_no = ?)";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, employeeNo);
        return count != null && count > 0;
    }

    @Transactional
    public int register(String employeeName, String phone, Long employeeNo, String password, Long positionId, String[] modulesIds) {
        // 参数验证
        if (employeeName == null || employeeName.trim().isEmpty()) {
            throw new BusinessException("职员名称不能为空");
        }
        if (phone == null || phone.trim().isEmpty()) {
            throw new BusinessException("手机号不能为空");
        }
        if (employeeNo == null) {
            throw new BusinessException("职员编号不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            throw new BusinessException("密码不能为空");
        }
        if (positionId == null) {
            throw new BusinessException("岗位ID不能为空");
        }
        if (modulesIds == null || modulesIds.length == 0) {
            throw new BusinessException("模块ID不能为空");
        }
        // 判断如果用户名已经存在,则提示用户名已经存在
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM employees WHERE employee_name = ?)", Integer.class, employeeName) > 0) {
            throw new BusinessException("用户名已经存在");
        }
        
        String insertEmployeeSql = "insert into employees(employee_name,phone,employee_no,password,position_id)values(?,?,?,?,?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(insertEmployeeSql, Statement.RETURN_GENERATED_KEYS);
            ps.setString(1, employeeName);
            ps.setString(2, phone);
            ps.setString(3, employeeNo.toString());
            ps.setString(4, password);
            ps.setInt(5, positionId.intValue());
            return ps;
        }, keyHolder);
        
        Number generatedIdNumber = keyHolder.getKey();
        if (generatedIdNumber == null) {
            throw new BusinessException("新增职员记录失败");
        }

        int employeeId = generatedIdNumber.intValue();

        String insertEmployeeModuleSql = "insert into employee_modules(employee_id,module_id)values(?,?)";
        for (String modulesId : modulesIds) {
            if (modulesId == null || modulesId.trim().isEmpty()) {
                throw new BusinessException("模块ID不能为空");
            }
            System.out.println("employeeId:" + employeeId + ",modulesId:" + modulesId);
            jdbcTemplate.update(insertEmployeeModuleSql, employeeId, Integer.valueOf(modulesId));
        }
        return employeeId;
    }

    /**
     * 递归向上查询用户的所有的上级,用 level 标记层级，自己是1，直接上级是2，以此类推
     */
    public List<Map<String, Object>> getUserALLUpLevel(Long userId) {
        // 参数验证
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 查询用户在哪个链C 链，或者B 链
        String sql = "SELECT chain_type FROM users WHERE id = ?";
        String chainType = jdbcTemplate.queryForObject(sql, String.class, userId);
        
        // 验证查询结果
        if (chainType == null || chainType.isEmpty()) {
            throw new BusinessException("未找到用户链类型");
        }
        
        sql = """
                WITH RECURSIVE user_hierarchy AS (
                    SELECT id, parent_id, 1 AS level
                    FROM users
                    WHERE id = ?\s
                    UNION ALL
                    SELECT u.id, u.parent_id, uh.level + 1
                    FROM users u
                    JOIN user_hierarchy uh ON u.id = uh.parent_id
                    WHERE uh.level < 10 \s
                ),
                filtered_hierarchy AS (
                    SELECT u.*, uh.level as original_level
                    FROM user_hierarchy uh
                    JOIN users u ON uh.id = u.id
                    WHERE u.status = 0 and u.chain_type =   ?\s
                )
                SELECT\s
                    id,mobile,avatar,sex,invite_code,qr_code,status,user_type,parent_id,chain_type,
                    ROW_NUMBER() OVER (ORDER BY original_level) AS level
                FROM\s
                    filtered_hierarchy
                ORDER BY\s
                    level""";
        return jdbcTemplate.queryForList(sql, userId, chainType);
    }


    /**
     * 用户登录
     */
    public Map<String, Object> login(String username, String password) {
        // 参数验证
        if (username == null || username.trim().isEmpty()) {
            throw new BusinessException("用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            throw new BusinessException("密码不能为空");
        }
        
        List<Map<String,Object>> userInfo = jdbcTemplate.queryForList("select phone,password from employees where employee_name = ? or phone = ?",username,username);
        if (userInfo.isEmpty()) {
            return null;
        }
        String uesrPassword = userInfo.get(0).get("password").toString();
        String phone = userInfo.get(0).get("phone").toString();
        String checkMd5 = md5Util.encryptMd5(phone,password);
        if(uesrPassword.equals(checkMd5)){
            String sql = "select e.id,e.employee_name,e.phone,e.employee_no,p.position_name,e.create_time,e.login_limit from employees e LEFT JOIN positions p ON e.position_id = p.id\n" +
                    "where (employee_name = ? or phone = ?)\n" +
                    "and password = ?";
            List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, username, username, checkMd5);
            return list.isEmpty() ? null : list.get(0);
        }else{
            return null;
        }
    }
    /**
     * 更新用户登录设备Mac 地址
     */
    public int updateLoginDevice(Long userId, String macAddress) {
        // 参数验证
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (macAddress == null || macAddress.trim().isEmpty()) {
            throw new BusinessException("MAC地址不能为空");
        }
        
        String sql = "UPDATE employees SET mac_address = ? WHERE id = ?";
        return jdbcTemplate.update(sql, macAddress, userId);
    }

    /**
     * 更新职员信息,岗位信息 和 模块信息
     */
    @Transactional
    public int updateEmployeeInfo(Long employeeId, String employeeName, String phone, String password, Long positionId, String[] modulesIds) {
        String sql = "";
        int rowsAffected = 0;
        // 拼接sql如果 password 为空，则不更新
        if (password == null || password.trim().isEmpty()) {
            sql = "UPDATE employees SET employee_name = ?, phone = ?, position_id = ? WHERE id = ?";
            rowsAffected = jdbcTemplate.update(sql, employeeName, phone, positionId, employeeId);
        }else{
            sql = "UPDATE employees SET employee_name = ?, phone = ?, password = ?, position_id = ? WHERE id = ?";
            rowsAffected = jdbcTemplate.update(sql, employeeName, phone, password, positionId, employeeId);
        }
        if (rowsAffected > 0) {
            sql = "DELETE FROM employee_modules WHERE employee_id = ?";
            jdbcTemplate.update(sql, employeeId);
            for (String modulesId : modulesIds) {
                if (modulesId == null || modulesId.trim().isEmpty()) {
                    throw new BusinessException("模块ID不能为空");
                }
                sql = "INSERT INTO employee_modules (employee_id, module_id) VALUES (?, ?)";
                jdbcTemplate.update(sql, employeeId, modulesId);
            }
        }else{
            throw new BusinessException("没有该职员信息");
        }
        return rowsAffected;
    }

    // 删除职员信息
    public int deleteEmployeeInfo(Long employeeId) {
        // 参数验证
        if (employeeId == null) {
            throw new BusinessException("职员ID不能为空");
        }
        
        String sql = "DELETE FROM employees WHERE id = ?";
        return jdbcTemplate.update(sql, employeeId);
    }

    /**
     * 搜索员工信息
     */
    public List<Map<String, Object>> searchEmployeeInfo(String employeeName, String phone) {

        StringBuilder sql = new StringBuilder("select e.id,e.employee_name,e.phone,e.employee_no,p.position_name,e.create_time,e.login_limit " +
                "from employees e LEFT JOIN positions p ON e.position_id = p.id where 1=1");
        
        List<Object> params = new ArrayList<>();
        
        if (employeeName != null && !employeeName.isEmpty()) {
            sql.append(" AND e.employee_name like ? ");
            params.add("%" + employeeName + "%");
        }
        
        if (phone != null && !phone.isEmpty()) {
            sql.append(" OR e.phone like ? ");
            params.add("%" + phone + "%");
        }
        return jdbcTemplate.queryForList(sql.toString(), params.toArray());
    }

    // 限制登录开关
    public int updateLoginLimit(Long employeeId, int loginLimit) {
        // 验证参数
        if (employeeId == null) {
            throw new BusinessException("职员ID不能为空");
        }
        if (loginLimit != 0 && loginLimit != 1) {
            throw new BusinessException("登录限制参数错误");
        }

        String sql = "UPDATE employees SET login_limit = ? WHERE id = ?";
        return jdbcTemplate.update(sql, loginLimit, employeeId);
    }
    
    /**
     * 获取所有员工信息，用于导出Excel
     */
    public List<Map<String, Object>> getAllEmployeesForExport() {
        String sql = "SELECT e.id, e.employee_name, e.phone, e.employee_no, p.position_name, " +
                     "CASE e.status WHEN 1 THEN '启用' WHEN 0 THEN '禁用' ELSE '未知' END AS status_text, " +
                     "e.create_time, e.update_time " +
                     "FROM employees e " +
                     "LEFT JOIN positions p ON e.position_id = p.id " +
                     "ORDER BY e.id";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 根据员工ID获取员工信息: 名字,手机号,工号,岗位ID,创建时间,限制登入
     */
    public Map<String, Object> getEmployeeById(Long employeeId) {
        // 使用左连接把 employees 和 positions 表关联
        String sql = "SELECT e.id, e.employee_name, e.phone, e.employee_no, e.position_id,p.position_name, e.create_time, e.login_limit\n" +
                "FROM employees e \n" +
                "LEFT JOIN positions p ON e.position_id = p.id \n" +
                "WHERE e.id = ?";
        return jdbcTemplate.queryForMap(sql, employeeId);
    }

    record ChainRow(
            String phone, String username, String inviter, String userType,
            int status, int flag, String jurisdiction, int fans,
            String address, String loginAddress, String level
    ) {}
    public static void main(String[] args) throws Exception {
        String url = "jdbc:mysql://************:3306/malla?useUnicode=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true";
        String user = "root";
        String pass = "123456";

        String sql = "WITH RECURSIVE\n" +
                "start_users AS (\n" +
                "  SELECT\n" +
                "    u.*,\n" +
                "    0 AS depth,\n" +
                "    JSON_ARRAY(CAST(u.id AS CHAR)) AS path_json,\n" +
                "    u.id AS root_id\n" +
                "  FROM mall_b_users u\n" +
                "  WHERE u.phone COLLATE utf8mb4_general_ci = ? COLLATE utf8mb4_general_ci\n" +
                "),\n" +
                "chain AS (\n" +
                "  SELECT * FROM start_users\n" +
                "  UNION ALL\n" +
                "  SELECT\n" +
                "    p.*,\n" +
                "    c.depth + 1 AS depth,\n" +
                "    JSON_ARRAY_APPEND(c.path_json, '$', CAST(p.id AS CHAR)) AS path_json,\n" +
                "    c.root_id AS root_id\n" +
                "  FROM chain c\n" +
                "  JOIN mall_b_users p\n" +
                "    ON p.id = c.parent_id\n" +
                "  WHERE\n" +
                "    c.depth + 1 <= COALESCE(?, 100)\n" +
                "    AND JSON_CONTAINS(c.path_json, JSON_QUOTE(p.id), '$') = 0\n" +
                "),\n" +
                "chain_dedup_rank AS (\n" +
                "  SELECT\n" +
                "    c.*,\n" +
                "    CASE\n" +
                "      WHEN COALESCE(TRIM(c.business_license), '') = '' THEN CONCAT('UNIQUE_', c.id)\n" +
                "      ELSE c.business_license\n" +
                "    END AS bl_key,\n" +
                "    ROW_NUMBER() OVER (\n" +
                "      PARTITION BY c.root_id,\n" +
                "                   CASE\n" +
                "                     WHEN COALESCE(TRIM(c.business_license), '') = '' THEN CONCAT('UNIQUE_', c.id)\n" +
                "                     ELSE c.business_license\n" +
                "                   END\n" +
                "      ORDER BY c.create_time ASC, c.id ASC\n" +
                "    ) AS bl_rn\n" +
                "  FROM chain c\n" +
                "),\n" +
                "chain_keep AS (\n" +
                "  SELECT cdr.*\n" +
                "  FROM chain_dedup_rank cdr\n" +
                "  WHERE cdr.bl_rn = 1\n" +
                "),\n" +
                "level_calc AS (\n" +
                "  SELECT\n" +
                "    ck.*,\n" +
                "    CASE\n" +
                "      WHEN UPPER(?) = 'C' THEN IF(ck.user_type = 'C', 1, 0)\n" +
                "      WHEN UPPER(?) = 'B' THEN IF(ck.user_type IN ('B','CB'), 1, 0)\n" +
                "      ELSE 0\n" +
                "    END AS type_flag\n" +
                "  FROM chain_keep ck\n" +
                "),\n" +
                "level_with_num AS (\n" +
                "  SELECT\n" +
                "    lc.*,\n" +
                "    CASE\n" +
                "      WHEN lc.type_flag = 1\n" +
                "        THEN SUM(lc.type_flag) OVER (\n" +
                "               PARTITION BY lc.root_id\n" +
                "               ORDER BY lc.depth ASC, lc.create_time ASC, lc.id ASC\n" +
                "             ) - 1\n" +
                "      ELSE NULL\n" +
                "    END AS final_level_num\n" +
                "  FROM level_calc lc\n" +
                ")\n" +
                "SELECT\n" +
                "  lw.phone           AS `手机号`,\n" +
                "  lw.username        AS `用户名`,\n" +
                "  lw.parent_username AS `邀请人`,\n" +
                "  lw.user_type       AS `用户类型`,\n" +
                "  lw.status          AS `状态`,\n" +
                "  lw.flag            AS `达标标志`,\n" +
                "  lw.jurisdiction    AS `权限`,\n" +
                "  lw.fans            AS `粉丝量`,\n" +
                "  lw.address         AS `地址`,\n" +
                "  lw.login_address   AS `登录地址`,\n" +
                "  CASE WHEN lw.type_flag = 1 THEN CAST(lw.final_level_num AS CHAR) ELSE '-' END AS `level`\n" +
                "FROM level_with_num lw\n" +
                "WHERE\n" +
                "  (\n" +
                "    ? IS NULL\n" +
                "    OR (lw.type_flag = 1 AND lw.final_level_num = ?)\n" +
                "    OR (lw.type_flag = 0)\n" +
                "  )\n" +
                "  AND lw.final_level_num = 7\n" +
                "ORDER BY\n" +
                "  lw.depth ASC,\n" +
                "  lw.create_time ASC,\n" +
                "  lw.id ASC;\n";

        String phone = "13999997773";
        String mode = "B";                 // "B" 或 "C"
        Integer targetLevel = null;        // null 表示整链
        Integer maxDepth = 100;

        try (Connection conn = DriverManager.getConnection(url, user, pass);
             PreparedStatement ps = conn.prepareStatement(sql)) {

            int i = 1;
            ps.setString(i++, phone);
            if (maxDepth == null) ps.setNull(i++, Types.INTEGER); else ps.setInt(i++, maxDepth);
            ps.setString(i++, mode);
            ps.setString(i++, mode);
            if (targetLevel == null) ps.setNull(i++, Types.INTEGER); else ps.setInt(i++, targetLevel);
            if (targetLevel == null) ps.setNull(i++, Types.INTEGER); else ps.setInt(i++, targetLevel);

            try (ResultSet rs = ps.executeQuery()) {
                List<ChainRow> rows = new ArrayList<>();
                while (rs.next()) {
                    rows.add(new ChainRow(
                            rs.getString("手机号"),
                            rs.getString("用户名"),
                            rs.getString("邀请人"),
                            rs.getString("用户类型"),
                            rs.getInt("状态"),
                            rs.getInt("达标标志"),
                            rs.getString("权限"),
                            rs.getInt("粉丝量"),
                            rs.getString("地址"),
                            rs.getString("登录地址"),
                            rs.getString("level")
                    ));
                }

                // 输出示例
                rows.forEach(r -> System.out.printf(
                        "%s | %s | %s | %s | level=%s%n",
                        r.phone(), r.username(), r.inviter(), r.userType(), r.level()
                ));
            }
        }
    }
}
